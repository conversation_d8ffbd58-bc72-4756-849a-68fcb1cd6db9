import React, { useState, useMemo } from 'react';
import { Card, Input, Button, Typography, List, Tag, Space, Alert, message } from 'antd';
import { PlusOutlined, DeleteOutlined, CalendarOutlined, UserOutlined, TrophyOutlined, DollarOutlined, HomeOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import type { WeeklyRegistration as WeeklyRegistrationType, Player, AppSettings } from '../types';
import CustomLabel from './CustomLabel';

dayjs.extend(weekOfYear);

const { Title } = Typography;

interface WeeklyRegistrationProps {
  settings: AppSettings;
  onRegistrationSubmit: (registration: WeeklyRegistrationType) => void;
}

const WeeklyRegistration: React.FC<WeeklyRegistrationProps> = ({
  settings,
  onRegistrationSubmit
}) => {
  const [playerName, setPlayerName] = useState<string>('');
  const [players, setPlayers] = useState<Player[]>([]);

  // Tự động lấy tuần tiếp theo (tuần sau tuần hiện tại)
  const nextWeek = useMemo(() => {
    return dayjs().add(1, 'week').startOf('week').add(1, 'day'); // Monday của tuần tiếp theo
  }, []);

  const getWeekDates = (weekDate: dayjs.Dayjs) => {
    const start = weekDate.startOf('week').add(1, 'day'); // Monday
    const end = weekDate.endOf('week').add(1, 'day'); // Sunday
    return { start: start.toDate(), end: end.toDate() };
  };

  // Tính toán thông tin phí khi số người thay đổi
  const registrationSummary = useMemo(() => {
    const totalPlayers = players.length;
    const maxPlayersWithDefaultCourts = settings.courtsCount * settings.playersPerCourt;
    const extraPlayersCount = Math.max(0, totalPlayers - maxPlayersWithDefaultCourts);
    const extraCourts = Math.ceil(extraPlayersCount / settings.playersPerCourt);
    const requiredCourts = settings.courtsCount + extraCourts;
    const totalExtraFee = extraCourts * settings.extraCourtFee;
    const feePerExtraPlayer = extraPlayersCount > 0 ? totalExtraFee / extraPlayersCount : 0;

    return {
      totalPlayers,
      requiredCourts,
      extraCourts,
      extraPlayersCount,
      totalExtraFee,
      feePerExtraPlayer
    };
  }, [players.length, settings]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  const formatDate = (date: dayjs.Dayjs) => {
    return date.format('DD/MM/YYYY');
  };

  const addPlayer = () => {
    if (playerName.trim()) {
      const newPlayer: Player = {
        id: Date.now().toString(),
        name: playerName.trim(),
        registeredAt: new Date()
      };
      setPlayers([...players, newPlayer]);
      setPlayerName('');
      message.success(`Đã thêm ${playerName.trim()}`);
    }
  };

  const removePlayer = (playerId: string) => {
    const player = players.find(p => p.id === playerId);
    setPlayers(players.filter(p => p.id !== playerId));
    if (player) {
      message.info(`Đã xóa ${player.name}`);
    }
  };

  const handleSubmit = () => {
    if (players.length > 0) {
      const { start, end } = getWeekDates(nextWeek);
      const registration: WeeklyRegistrationType = {
        id: Date.now().toString(),
        weekStart: start,
        weekEnd: end,
        players: players,
        settings: { ...settings }
      };
      onRegistrationSubmit(registration);
      setPlayers([]);
      message.success('Đăng ký thành công!');
    }
  };

  return (
    <Card className="fade-in-up">
      <div style={{
        display: 'flex',
        alignItems: 'center',
        marginBottom: '24px',
        gap: '16px'
      }}>
        <div style={{
          width: '44px',
          height: '44px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)',
          borderRadius: '12px',
          color: 'white',
          fontSize: '22px',
          boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)'
        }}>
          <TrophyOutlined />
        </div>
        <Title level={2} className="mb-0" style={{
          color: '#1890ff',
          fontSize: '26px',
          fontWeight: 600,
          lineHeight: 1.2,
          marginTop: '2px'
        }}>
          Đăng ký đánh cầu lông
        </Title>
      </div>

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* Hiển thị tuần đăng ký tự động */}
        <div>
          <CustomLabel icon={<CalendarOutlined />}>
            Tuần đăng ký
          </CustomLabel>
          <Alert
            message={`Đăng ký cho tuần: ${formatDate(nextWeek)} - ${formatDate(nextWeek.endOf('week').add(1, 'day'))}`}
            type="info"
            showIcon
            style={{
              background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
              border: '2px solid #1890ff',
              borderRadius: '12px',
              fontSize: '16px',
              fontWeight: '600'
            }}
          />
        </div>

        <div>
          <CustomLabel icon={<UserOutlined />}>
            Thêm người chơi
          </CustomLabel>
          <Space.Compact style={{ width: '100%' }}>
            <Input
              value={playerName}
              onChange={(e) => setPlayerName(e.target.value)}
              placeholder="Nhập tên người chơi"
              prefix={<UserOutlined />}
              onPressEnter={addPlayer}
              size="large"
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={addPlayer}
              disabled={!playerName.trim()}
              size="large"
            >
              Thêm
            </Button>
          </Space.Compact>
        </div>

        {players.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-3">
              <Title level={4} className="mb-0">
                Danh sách đăng ký
              </Title>
              <Tag color="blue">{players.length} người</Tag>
            </div>
            <List
              size="small"
              bordered
              dataSource={players}
              style={{
                maxHeight: 200,
                overflowY: 'auto',
                overflowX: 'hidden',
                wordBreak: 'break-word'
              }}
              renderItem={(player, index) => (
                <List.Item
                  actions={[
                    <Button
                      type="text"
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={() => removePlayer(player.id)}
                    >
                      Xóa
                    </Button>
                  ]}
                >
                  <span>{index + 1}. {player.name}</span>
                </List.Item>
              )}
            />
          </div>
        )}

        {/* Hiển thị thông tin phí khi có người vượt quá kế hoạch */}
        {players.length > 0 && (
          <div>
            <CustomLabel icon={<HomeOutlined />}>
              Thông tin sân và phí
            </CustomLabel>

            {registrationSummary.extraCourts > 0 ? (
              <Alert
                message="⚠️ Cần thuê thêm sân"
                description={
                  <div style={{ marginTop: '8px' }}>
                    <p><strong>Số người đăng ký:</strong> {registrationSummary.totalPlayers} người</p>
                    <p><strong>Sân cần thiết:</strong> {registrationSummary.requiredCourts} sân (gồm {settings.courtsCount} sân mặc định + {registrationSummary.extraCourts} sân thêm)</p>
                    <p><strong>Số người vượt quá:</strong> {registrationSummary.extraPlayersCount} người</p>
                    <p style={{ color: '#f5222d', fontWeight: 'bold', fontSize: '16px', marginTop: '12px' }}>
                      <DollarOutlined /> <strong>Tổng phí thêm:</strong> {formatCurrency(registrationSummary.totalExtraFee)}
                    </p>
                    <p style={{ color: '#f5222d', fontWeight: 'bold', fontSize: '14px' }}>
                      <strong>Phí/người vượt:</strong> {formatCurrency(registrationSummary.feePerExtraPlayer)}
                    </p>
                  </div>
                }
                type="warning"
                showIcon
                style={{
                  background: 'linear-gradient(135deg, #fff7e6 0%, #ffd591 100%)',
                  border: '2px solid #fa8c16',
                  borderRadius: '12px'
                }}
              />
            ) : (
              <Alert
                message="✅ Không cần thuê thêm sân"
                description={
                  <div style={{ marginTop: '8px' }}>
                    <p><strong>Số người đăng ký:</strong> {registrationSummary.totalPlayers} người</p>
                    <p><strong>Sân cần thiết:</strong> {registrationSummary.requiredCourts} sân</p>
                    <p style={{ color: '#52c41a', fontWeight: 'bold' }}>Số người vừa đủ với {settings.courtsCount} sân hiện có!</p>
                  </div>
                }
                type="success"
                showIcon
                style={{
                  background: 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',
                  border: '2px solid #52c41a',
                  borderRadius: '12px'
                }}
              />
            )}
          </div>
        )}

        <Button
          type="primary"
          size="large"
          block
          onClick={handleSubmit}
          disabled={players.length === 0}
        >
          Đăng ký tuần tiếp theo ({players.length} người)
        </Button>
      </Space>
    </Card>
  );
};

export default WeeklyRegistration;
